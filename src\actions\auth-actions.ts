"use server"

// Authentication actions for Trustay API
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import {
  LoginRequest,
  RegisterRequest,
  RegisterDirectRequest,
  ChangePasswordRequest,
  AuthResponse,
  VerificationResponse,
  PasswordStrengthResponse,
  GeneratePasswordResponse,
  UserProfile,
} from '../types/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Helper function to make API calls
async function apiCall<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get('accessToken')?.value;

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (accessToken) {
    headers['Authorization'] = `Bearer ${accessToken}`;
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  let data;
  try {
    data = await response.json();
  } catch {
    data = null;
  }

  if (!response.ok) {
    throw new ApiError(
      data?.message || data?.error || `HTTP ${response.status}`,
      response.status,
      data
    );
  }

  return data;
}

// Helper function to set auth cookies
async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = await cookies();

  cookieStore.set('accessToken', accessToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });

  cookieStore.set('refreshToken', refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  });
}

// Send email verification code
export const sendEmailVerification = async (
  email: string
): Promise<VerificationResponse> => {
  return await apiCall<VerificationResponse>('/api/verification/send', {
    method: 'POST',
    body: JSON.stringify({
      type: 'email',
      email,
    }),
  });
};

// Verify email code
export const verifyEmailCode = async (
  email: string,
  code: string
): Promise<VerificationResponse> => {
  return await apiCall<VerificationResponse>('/api/verification/verify', {
    method: 'POST',
    body: JSON.stringify({
      type: 'email',
      email,
      code,
    }),
  });
};

// Register with verification
export const registerWithVerification = async (
  userData: RegisterRequest,
  verificationToken: string
): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/register', {
    method: 'POST',
    headers: {
      'X-Verification-Token': verificationToken,
    },
    body: JSON.stringify(userData),
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Register direct (for development)
export const registerDirect = async (
  userData: RegisterDirectRequest
): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/register-direct', {
    method: 'POST',
    body: JSON.stringify(userData),
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Login
export const login = async (credentials: LoginRequest): Promise<AuthResponse> => {
  const response = await apiCall<AuthResponse>('/api/auth/login', {
    method: 'POST',
    body: JSON.stringify(credentials),
  });

  // Store tokens in cookies
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Get current user
export const getCurrentUser = async (): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/auth/me', {
    method: 'GET',
  });
};

// Refresh token
export const refreshToken = async (): Promise<AuthResponse> => {
  const cookieStore = await cookies();
  const refreshTokenValue = cookieStore.get('refreshToken')?.value;

  if (!refreshTokenValue) {
    throw new ApiError('No refresh token available', 401);
  }

  const response = await apiCall<AuthResponse>('/api/auth/refresh', {
    method: 'POST',
    body: JSON.stringify({
      refreshToken: refreshTokenValue,
    }),
  });

  // Update stored tokens
  await setAuthCookies(response.access_token, response.refresh_token);

  return response;
};

// Update user profile
export const updateUserProfile = async (
  profileData: any
): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/users/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData),
  });
};

// Logout
export const logout = async (): Promise<void> => {
  const cookieStore = await cookies();
  cookieStore.delete('accessToken');
  cookieStore.delete('refreshToken');
};

// Complete registration and redirect
export async function completeRegistration(formData: FormData) {
  const role = formData.get('role') as string;

  if (role === 'tenant') {
    redirect('/dashboard/tenant');
  } else if (role === 'landlord') {
    redirect('/dashboard/landlord');
  } else {
    redirect('/');
  }
}

// Skip profile update and redirect to home
export async function skipProfileUpdate() {
  redirect('/');
}
