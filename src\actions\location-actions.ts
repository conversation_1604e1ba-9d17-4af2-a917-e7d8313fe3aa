"use server"

// Location services actions for Trustay API
import { Province, District, Ward } from '../types/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Helper function to make API calls
async function apiCall<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  let data;
  try {
    data = await response.json();
  } catch {
    data = null;
  }

  if (!response.ok) {
    throw new ApiError(
      data?.message || data?.error || `HTTP ${response.status}`,
      response.status,
      data
    );
  }

  return data;
}

// Get all provinces
export const getProvinces = async (): Promise<Province[]> => {
  return await apiCall<Province[]>('/api/provinces', {
    method: 'GET',
  });
};

// Get districts by province
export const getDistrictsByProvince = async (provinceId: string): Promise<District[]> => {
  return await apiCall<District[]>(`/api/districts?provinceId=${provinceId}`, {
    method: 'GET',
  });
};

// Get wards by district
export const getWardsByDistrict = async (districtId: string): Promise<Ward[]> => {
  return await apiCall<Ward[]>(`/api/wards?districtId=${districtId}`, {
    method: 'GET',
  });
};
